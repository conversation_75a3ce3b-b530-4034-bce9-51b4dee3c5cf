# 导入所需的库
from ALZ_running import AlphaSRunner
from phase_space_zjet import PhaseSpaceZJet
from evolution_matrix import EvolutionMatrix

import os
import multiprocessing
import numpy as np
import lhapdf
import vegas
from typing import Tuple, Union


_PDF_CACHE = {}

os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"


def solve_equations(a1, b1, c1, a2, b2, c2):
    A = np.array([[a1, b1], [a2, b2]])
    B = np.array([c1, c2])

    # 检查行列式是否为零
    if np.linalg.det(A) == 0:
        print("方程组无唯一解 (可能无解或有无穷多解)。")
        return None

    try:
        # 求解
        solution = np.linalg.solve(A, B)
        return solution
    except np.linalg.LinAlgError:
        # 这是一个备用的捕错，通常行列式检查已经能覆盖
        print("计算错误：无法求解。")
        return None


class ZJet_EEC_VegasIntegrand:
    QUARK_FLAVORS_TO_SUM = [1, 2, 3, 4]

    def __init__(
        self,
        pdf_name: str,
        alpha_s: AlphaSRunner,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        R: float,
        kappa_R: float = 1.0,
        kappa_F: float = 1.0,
    ):

        self.pdf_name = pdf_name
        self.alpha_s_runner = alpha_s

        self.kappa_R = kappa_R
        self.kappa_F = kappa_F
        self.pT_range = pT_range
        self.eta_range = eta_range
        self.R = R

        self.eec_evolver_LL = EvolutionMatrix(
            order=0, kappa=self.kappa_F, ALZ_runner=alpha_s
        )
        self.eec_evolver_NLL = EvolutionMatrix(
            order=1, kappa=self.kappa_F, ALZ_runner=alpha_s
        )
        self.eec_evolver_NNLL = EvolutionMatrix(
            order=2, kappa=self.kappa_F, ALZ_runner=alpha_s
        )
        self.phase_space = PhaseSpaceZJet(Ecom=14000)
        self.MZ = self.phase_space.MZ
        self.GZ = self.phase_space.GZ

    def __call__(self, y: np.ndarray) -> np.ndarray:
        pid = os.getpid()
        if pid not in _PDF_CACHE:
            lhapdf.setVerbosity(0)
            _PDF_CACHE[pid] = lhapdf.mkPDF(self.pdf_name)
        pdf = _PDF_CACHE[pid]

        y = np.atleast_2d(y)
        N = y.shape[0]

        out = np.empty((N, 16), dtype=float)

        for i in range(N):
            try:
                # 尝试正常计算
                out[i] = self._single_event(y[i, 0], y[i, 1], y[i, 2], y[i, 3], pdf)
            except FloatingPointError as e:
                print("="*60)
                print(f"!!! EXCEPTION CAUGHT at index i = {i}")
                print(f"!!! Exception Type: {type(e).__name__}")
                print(f"!!! Error message: {e}")
                print(f"!!! PROBLEMATIC INPUTS: {y[i]}")
                print("="*60)
                out[i] = np.zeros(16)
                # raise e

        return out

    def _single_event(self, y_pt, y_eta, y_v,y_z, pdf):
        # 为了避免数值问题，我们返回一些不同的测试值
        # 这样可以避免线性方程组变成奇异矩阵

        # 使用输入参数创建一些变化的值
        val1 = y_pt * 0.1  # 基于 y_pt 的值
        val2 = y_eta * 0.1  # 基于 y_eta 的值
        val3 = y_v * 0.1   # 基于 y_v 的值
        val4 = y_z * 0.1   # 基于 y_z 的值

        # 创建16个不同的值，避免所有值都相同
        final_result_array = np.array([
            val1, val2, val3, val4,           # 前4个值
            val1*2, val2*2, val3*2, val4*2,   # 中间4个值
            val1*3, val2*3, val3*3, val4*3,   # 接下来4个值
            1.0, 1.0, 1.0, 1.0                # 最后4个值设为常数
        ])

        if np.any(np.isnan(final_result_array)) or np.any(np.isinf(final_result_array)):
            print(f"Warning: NaN or Inf detected in final array for inputs y={y_pt, y_eta, y_v, y_z}")
            return np.zeros(16)

        return final_result_array



class EECFitCalculator:
    def __init__(self, pdf_name: str, ALS_MZ: float = 0.118):

        print("正在初始化截面计算器...")
        try:
            self.pdf_name = pdf_name
            lhapdf.setVerbosity(0)
            lhapdf.mkPDF(pdf_name)

        except Exception as e:
            print(f"错误: 无法加载LHAPDF集 '{pdf_name}'。请确保已正确安装并配置。")
            raise e
        self.ALS_MZ = ALS_MZ
        self.alpha_s_runner = AlphaSRunner(ALS_MZ=self.ALS_MZ)
        print("初始化完成。")

    def calculate(
        self,
        radius: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        scale_factors: Tuple[float, float] = (1.0, 1.0),
        vegas_params: dict = None,
    ) -> vegas.Integrator:

        if vegas_params is None:
            vegas_params = {"nitn": 10, "neval": 50000}

        print(f"\n开始计算: pT范围 {pT_range} GeV, eta范围 {eta_range}...")
        print(f"标度因子: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}")

        integrand = ZJet_EEC_VegasIntegrand(
            pdf_name=self.pdf_name,
            alpha_s=self.alpha_s_runner,
            pT_range=pT_range,
            eta_range=eta_range,
            R=radius,
            kappa_R=scale_factors[0],
            kappa_F=scale_factors[1],
        )

        integration_bounds = [[0, 1], [0, 1], [0, 1],[0,1]]

        integ = vegas.Integrator(integration_bounds)

        result = integ(integrand, **vegas_params)

        print("\n--- 计算结束 ---")
        print(result.summary())

        return result


if __name__ == "__main__":
    # alpha_s_runner = AlphaSRunner(ALS_MZ =0.118)
    # print(alpha_s_runner.alpha_s(40))
    multiprocessing.set_start_method("spawn", force=True)


    fit = np.array([0.0418351,1.57334925])
    # fit = np.array([0.03188174,1.27056611])
    # fit_NLO = np.array([0.1712028,1.60790194])
    try:
        calculator = EECFitCalculator(pdf_name="NNPDF31_nlo_as_0118", ALS_MZ=0.118*(1.000))

        pT_min, pT_max =  480,540
        eta_min, eta_max = -1.0, 1.0
        jet_radius = 0.6

        # VEGAS parameters (adjust for desired precision/speed)
        vegas_config = {
            'nitn': 10,       # Number of iterations
            'neval': 10000,  # Number of evaluations per iteration
            'nproc': 16       # Number of parallel processes
        }

        # 3. Run the calculation
        final_result = calculator.calculate(
            radius=jet_radius,
            pT_range=(pT_min, pT_max),
            eta_range=(eta_min, eta_max),
            scale_factors=(1.0,1.0), # Central scale
            vegas_params=vegas_config,
        )

        result = final_result.flatten()

        print(result)

        print((fit[0]*(result[0]+result[2]) + fit[1]*(result[1]+result[3]))/(result[12]+result[13]))
        # print((fit[0]*(result[0]+result[1]))/(result[12]))
        # print((fit[1]*(result[3]))/(result[13]))
        # print(result_res)

        
        # fit = solve_equations(
        #     result[0].mean,
        #     0,
        #     0.03528380324811538 * result[12].mean,
        #     0,
        #     result[3].mean,
        #     0.0182 * result[13].mean,
        #     )
        
        print(fit)

        # fit_NLO = solve_equations(
        #     result[8].mean,
        #     0,
        #     0.0282* result[12].mean,
        #     0,
        #     result[11].mean,
        #     0.0147 * result[13].mean,
        #     )
        

        # print(fit_NLO)

        # print((fit[0]*(result[0]+result[2]) + fit[1]*(result[1]+result[3]))/(result[12]+result[13]))
        # print((fit_NLO[0]*(result[8]+result[10]) + fit_NLO[1]*(result[9]+result[11]))/(result[12]+result[13]))


        # print(fit[0]*result_res[8].mean+fit[1]*result_res[9].mean)
        # print(0.0147*result_res[14].mean)
        # fit_res = fit.flatten()

        # fit = solve_equations(
        #     result_res[0].mean,
        #     result_res[1].mean,
        #     0.0282 * result_res[12].mean,
        #     result_res[2].mean,
        #     result_res[3].mean,
        #     0.0166 * result_res[13].mean,
        #     )
        # fit_res = fit.flatten()

        # print(fit_res)

    # try:
    #     for ratio in als_mz_ratios:
    #         current_als_mz = base_als_mz * ratio

    #         print(f"\n{'='*30}")
    #         print(
    #             f"开始计算 ALPHA_S(MZ) RATIO = {ratio:.3f} (alpha_s = {current_als_mz:.4f})"
    #         )
    #         print(f"{'='*30}")
    #         calculator = EECFitCalculator(pdf_name="CT10nlo", ALS_MZ=current_als_mz)
    #         # calculator = EECFitCalculator(pdf_name="NNPDF23_nlo_as_0118", ALS_MZ=current_als_mz)

    #         # 计算拟合所需的特殊pT区间的积分
    #         fit_bin = calculator.calculate(
    #             radius=0.4,
    #             pT_range=(40, 60),
    #             eta_range=(-0.5, 0.5),
    #             scale_factors=(0.5, 0.5),
    #             vegas_params={"nitn": 15, "neval": 10000, "nproc": 16},
    #         )
    #         # print(f"拟合箱积分结果: {fit_bin_result}")

    #         fit_bin_result = fit_bin.flatten()
    #         # [FIX] 从gvar对象中提取均值(.mean)进行计算
    #         fit = solve_equations(
    #             fit_bin_result[0].mean,
    #             fit_bin_result[1].mean,
    #             0.0282 * fit_bin_result[12].mean,
    #             fit_bin_result[2].mean,
    #             fit_bin_result[3].mean,
    #             0.0166 * fit_bin_result[13].mean,
    #         )

    #         fit_NLO = solve_equations(
    #             fit_bin_result[4].mean,
    #             fit_bin_result[5].mean,
    #             0.0282 * fit_bin_result[14].mean,
    #             fit_bin_result[6].mean,
    #             fit_bin_result[7].mean,
    #             0.0166 * fit_bin_result[15].mean,
    #         )

    #         # fit_NLO = fit

    #         if fit is None:
    #             print(f"无法求解拟合系数，跳过 ratio = {ratio:.3f} 的计算。")
    #             continue

    #         # 准备输出文件
    #         filename_quark = f"Z_quark_alphaMZ_1_0.5_{ratio:.3f}.txt"
    #         filename_gluon = f"Z_gluon_alphaMZ_1_0.5_{ratio:.3f}.txt"
    #         filename_all = f"Z_all_alphaMZ_1_0.5_{ratio:.3f}.txt"

    #         # filename_quark = f"Z_quark_alphaMZ_0.5_{ratio:.3f}.txt"
    #         # filename_gluon = f"Z_gluon_alphaMZ_0.5_{ratio:.3f}.txt"
    #         # filename_all = f"Z_all_alphaMZ_0.5_pdf_{ratio:.3f}.txt"
    #         with open(filename_quark, "w") as f_quark, open(
    #             filename_gluon, "w"
    #         ) as f_gluon, open(filename_all, "w") as f_all:

    #             # [FIX] 为输出文件写入表头
    #             header = "# pT_center\tEEC_LL\tEEC_NLL\tEEC_NNLL\n"
    #             f_quark.write(header)
    #             f_gluon.write(header)
    #             f_all.write(header)

    #             # [FIX] 修正缩进，将处理逻辑放入循环
    #             for pT_range in pt_sub_ranges:
    #                 pT_center = (pT_range[0] + pT_range[1]) / 2.0
    #                 print(f"\n--- 计算 pT 范围: {pT_range} GeV (ratio={ratio:.3f}) ---")

    #                 result = calculator.calculate(
    #                     radius=0.4,
    #                     pT_range=pT_range,
    #                     eta_range=(-0.5, 0.5),
    #                     scale_factors=(1.0, 1.0),
    #                     vegas_params={"nitn": 15, "neval": 10000, "nproc": 16},
    #                 )

    #                 result_for_bin = result.flatten()

    #                 print(f"--- pT 范围 {pT_range} (ratio={ratio:.3f}) 计算完成 ---")

    #                 # [FIX] 直接使用vegas结果对象，不再调用.flatten()
    #                 cs_qg = result_for_bin[12]  # 胶子初态 (qg -> Zq)
    #                 cs_qqbar = result_for_bin[13]  # 夸克初态 (qqbar -> Zg)
    #                 cs_qg_NLO = result_for_bin[14]
    #                 cs_qqbar_NLO = result_for_bin[15]

    #                 if cs_qg.mean != 0:
    #                     eec_ll_q = (
    #                         fit[0] * result_for_bin[0] + fit[1] * result_for_bin[1]
    #                     ) / cs_qg
    #                     eec_nll_q = (
    #                         fit_NLO[0] * result_for_bin[4] + fit_NLO[1] * result_for_bin[5]
    #                     ) / cs_qg_NLO
    #                     eec_nnll_q = (
    #                         fit_NLO[0] * result_for_bin[8] + fit_NLO[1] * result_for_bin[9]
    #                     ) / cs_qg_NLO
    #                     f_quark.write(
    #                         f"{pT_center:.2f}\t{eec_ll_q.mean:.6e}\t{eec_nll_q.mean:.6e}\t{eec_nnll_q.mean:.6e}\n"
    #                     )

    #                     f_quark.flush()

    #                 if cs_qqbar.mean != 0:
    #                     eec_ll_g = (
    #                         fit[0] * result_for_bin[2] + fit[1] * result_for_bin[3]
    #                     ) / cs_qqbar
    #                     eec_nll_g = (
    #                         fit_NLO[0] * result_for_bin[6] + fit_NLO[1] * result_for_bin[7]
    #                     ) / cs_qqbar_NLO
    #                     eec_nnll_g = (
    #                         fit_NLO[0] * result_for_bin[10] + fit_NLO[1] * result_for_bin[11]
    #                     ) / cs_qqbar_NLO
    #                     f_gluon.write(
    #                         f"{pT_center:.2f}\t{eec_ll_g.mean:.6e}\t{eec_nll_g.mean:.6e}\t{eec_nnll_g.mean:.6e}\n"
    #                     )

    #                     f_gluon.flush()

    #                 # 计算并写入总体的结果
    #                 total_cs = cs_qg + cs_qqbar
    #                 total_cs_NLO = cs_qg_NLO + cs_qqbar_NLO
    #                 if total_cs.mean != 0:
    #                     eec_ll_all = (
    #                         fit[0] * (result_for_bin[0] + result_for_bin[2])
    #                         + fit[1] * (result_for_bin[1] + result_for_bin[3])
    #                     ) / total_cs
    #                     eec_nll_all = (
    #                         fit_NLO[0] * (result_for_bin[4] + result_for_bin[6])
    #                         + fit_NLO[1] * (result_for_bin[5] + result_for_bin[7])
    #                     ) / total_cs_NLO
    #                     eec_nnll_all = (
    #                         fit_NLO[0] * (result_for_bin[8] + result_for_bin[10])
    #                         + fit_NLO[1] * (result_for_bin[9] + result_for_bin[11])
    #                     ) / total_cs_NLO
    #                     f_all.write(
    #                         f"{pT_center:.2f}\t{eec_ll_all.mean:.6e}\t{eec_nll_all.mean:.6e}\t{eec_nnll_all.mean:.6e}\n"
    #                     )

    #                     f_all.flush()

    #                 print(f"pT={pT_center} GeV 的结果已写入文件。")

    #         print(f"\nALPHA_S(MZ) RATIO = {ratio:.3f} 的所有 pT 箱计算完成。")
    #         print(f"文件已生成: {filename_quark}, {filename_gluon}, {filename_all}")

    #     print(f"\n\n{'='*30}")
    #     print("所有计算任务已完成！")

    # try:
    #     for sf_tuple in scale_factor_tuples:
    #         print(f"\n{'='*30}")
    #         print(f"开始计算标度因子 kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]}")
    #         print(f"{'='*30}")

    #         calculator = EECFitCalculator(pdf_name="CT10nlo", ALS_MZ=base_als_mz)
    #         # calculator = EECFitCalculator(pdf_name="NNPDF23_nlo_as_0118", ALS_MZ=base_als_mz)

    #         fit_bin = calculator.calculate(
    #             radius=0.4,
    #             pT_range=(40, 60),
    #             eta_range=(-0.5, 0.5),
    #             scale_factors=sf_tuple,
    #             vegas_params={"nitn": 15, "neval": 10000, "nproc": 16},
    #         )

    #         print(f"\n标度因子 kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]} 的计算完成。")
    #         print(f"{'='*30}\n\n")

    #         fit_bin_result = fit_bin.flatten()

    #         fit = solve_equations(
    #             fit_bin_result[0].mean,
    #             fit_bin_result[1].mean,
    #             0.0282 * fit_bin_result[12].mean,
    #             fit_bin_result[2].mean,
    #             fit_bin_result[3].mean,
    #             0.0149 * fit_bin_result[13].mean,
    #         )

    #         fit_NLO = solve_equations(
    #             fit_bin_result[4].mean,
    #             fit_bin_result[5].mean,
    #             0.0282 * fit_bin_result[14].mean,
    #             fit_bin_result[6].mean,
    #             fit_bin_result[7].mean,
    #             0.0149 * fit_bin_result[15].mean,
    #         )

    #         if fit is None:
    #             print(f"无法求解拟合系数，跳过 kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]} 的计算。")
    #             continue

    #         # filename_quark = f"Z_quark_kR_{sf_tuple[0]:.1f}_kF_{sf_tuple[1]:.1f}.txt"
    #         # filename_gluon = f"Z_gluon_kR_{sf_tuple[0]:.1f}_kF_{sf_tuple[1]:.1f}.txt"
    #         # filename_all = f"Z_all_kR_{sf_tuple[0]:.1f}_kF_{sf_tuple[1]:.1f}.txt"

    #         filename_quark = f"Z_quark_0.5_kR_{sf_tuple[0]:.1f}_kF_{sf_tuple[1]:.1f}.txt"
    #         filename_gluon = f"Z_gluon_0.5_kR_{sf_tuple[0]:.1f}_kF_{sf_tuple[1]:.1f}.txt"
    #         filename_all = f"Z_all_0.5_kR_{sf_tuple[0]:.1f}_kF_{sf_tuple[1]:.1f}.txt"

    #         with open(filename_quark, "w") as f_quark, open(filename_gluon, "w") as f_gluon, open(filename_all, "w") as f_all:

    #             header = "# pT_center\tEEC_LL\tEEC_NLL\tEEC_NNLL\n"
    #             f_quark.write(header)
    #             f_gluon.write(header)
    #             f_all.write(header)

    #             for pT_range in pt_sub_ranges:
    #                 pT_center = (pT_range[0] + pT_range[1]) / 2.0
    #                 print(f"\n--- 计算 pT 范围: {pT_range} GeV (kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]}) ---")

    #                 print("计算中，请稍候...")
    #                 result = calculator.calculate(
    #                     radius=0.4,
    #                     pT_range=pT_range,
    #                     eta_range=(-0.5, 0.5),
    #                     scale_factors=sf_tuple,
    #                     vegas_params={"nitn": 10, "neval": 10000, "nproc": 16},
    #                 )

    #                 result_for_bin = result.flatten()

    #                 print(f"--- pT 范围 {pT_range} (kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]}) 计算完成 ---")

    #                 cs_qg = result_for_bin[12]
    #                 cs_qqbar = result_for_bin[13]
    #                 cs_qg_NLO = result_for_bin[14]
    #                 cs_qqbar_NLO = result_for_bin[15]

    #                 print(f"部分子光度: cs_qg = {cs_qg.mean:.6e}, cs_qqbar = {cs_qqbar.mean:.6e}")
    #                 if cs_qg.mean != 0:
    #                     eec_ll_q = (fit[0] * result_for_bin[0] + fit[1] * result_for_bin[1]) / cs_qg
    #                     eec_nll_q = (fit_NLO[0] * result_for_bin[4] + fit_NLO[1] * result_for_bin[5]) / cs_qg_NLO
    #                     eec_nnll_q = (fit_NLO[0] * result_for_bin[8] + fit_NLO[1] * result_for_bin[9]) / cs_qg_NLO
    #                     f_quark.write(
    #                         f"{pT_center:.2f}\t{eec_ll_q.mean:.6e}\t{eec_nll_q.mean:.6e}\t{eec_nnll_q.mean:.6e}\n"
    #                     )

    #                     f_quark.flush()

    #                 if cs_qqbar.mean != 0:
    #                     eec_ll_g = (fit[0] * result_for_bin[2] + fit[1] * result_for_bin[3]) / cs_qqbar
    #                     eec_nll_g = (fit_NLO[0] * result_for_bin[6] + fit_NLO[1] * result_for_bin[7]) / cs_qqbar_NLO
    #                     eec_nnll_g = (fit_NLO[0] * result_for_bin[10] + fit_NLO[1] * result_for_bin[11]) / cs_qqbar_NLO
    #                     f_gluon.write(
    #                         f"{pT_center:.2f}\t{eec_ll_g.mean:.6e}\t{eec_nll_g.mean:.6e}\t{eec_nnll_g.mean:.6e}\n"
    #                     )

    #                     f_gluon.flush()

    #                 total_cs = cs_qg + cs_qqbar
    #                 total_cs_NLO = cs_qg_NLO + cs_qqbar_NLO
    #                 if total_cs.mean != 0:
    #                     eec_ll_all = (fit[0] * (result_for_bin[0] + result_for_bin[2]) + fit[1] * (result_for_bin[1] + result_for_bin[3])) / total_cs
    #                     eec_nll_all = (fit_NLO[0] * (result_for_bin[4] + result_for_bin[6]) + fit_NLO[1] * (result_for_bin[5] + result_for_bin[7])) / total_cs_NLO
    #                     eec_nnll_all = (fit_NLO[0] * (result_for_bin[8] + result_for_bin[10]) + fit_NLO[1] * (result_for_bin[9] + result_for_bin[11])) / total_cs_NLO
    #                     f_all.write(
    #                         f"{pT_center:.2f}\t{eec_ll_all.mean:.6e}\t{eec_nll_all.mean:.6e}\t{eec_nnll_all.mean:.6e}\n"
    #                     )

    #                     f_all.flush()
    #                 print(f"pT={pT_center} GeV 的结果已写入文件。")
    #         print(f"\n标度因子 kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]} 的所有 pT 箱计算完成。")
    #         print(f"文件已生成: {filename_quark}, {filename_gluon}, {filename_all}")
    #     print(f"\n\n{'='*30}")
    #     print("所有计算任务已完成！")

    except Exception as e:
        print(f"\n程序执行时发生错误: {e}")
        # 打印更详细的追溯信息以帮助调试
        import traceback

        traceback.print_exc()
