import vegas
import numpy as np

# 定义我们的“测试函数” g(x)
def g(x):
    return x**2

# 定义 Plus 分布中发散的部分 f(x)
# 注意：我们实际上不会直接在积分器中使用这个函数
# def f(x):
#     return 1 / (1 - x)

# 定义要传递给 vegas 的被积函数
# 这是根据 Plus 分布的定义转化后的形式：f(x) * (g(x) - g(1))
def integrand(x):
    # vegas 传递的 x 是一个 numpy 数组
    # 为了数值稳定性，我们避免直接计算 0/0 的情况。
    # 对于这个特定的例子，我们可以进行解析化简。
    # (x^2 - 1) / (1 - x) = (x-1)(x+1) / -(x-1) = -(x+1)
    # 这种化简是处理数值奇点最稳健的方法。
    return -(x + 1)

# 如果不能解析化简，对于更复杂的情况，可以处理 x 接近 1 的极限
# def integrand_general(x):
#     # 避免当 x=1 时出现 0/0
#     # 使用 np.isclose 来安全地比较浮点数
#     if np.any(np.isclose(x, 1.0)):
#         # 当 x->1 时, (g(x) - g(1)) / (1-x) 的极限是 -g'(1)
#         # g'(x) = 2x, 所以 g'(1) = 2
#         # 整个被积函数的极限是 -2
#         # 这里我们直接返回解析化简后的结果，因为它在所有点都成立
#         return -(x + 1)
#     else:
#         return (g(x) - g(1)) / (1 - x)


# 创建一个 vegas 积分器，积分区间为 [0, 1]
integ = vegas.Integrator([[0, 1]])

# 运行积分
# nitn 是预热迭代次数，neval 是每次迭代的函数调用次数
result = integ(integrand, nitn=20, neval=20000)

# 打印结果
print("Vegas 积分结果:")
print(result.summary())
print(f"数值结果 = {result.mean}")
print(f"解析结果 = -1.5")